"""
Module for handling MCQ translation log operations.
"""

import logging
import uuid
import datetime
from contextlib import contextmanager
from typing import Optional

from db_config.db import get_engine, LOG_SCHEMA

# Get logger instance
logger = logging.getLogger(__name__)

class MCQTranslationLog:
    """
    Class for handling MCQ translation log operations.
    Uses wslog.mcq_translation_log table to track translation tasks.
    """

    @contextmanager
    def get_connection(self):
        """
        Context manager to get a direct database connection.
        This ensures the connection is properly closed after use.
        """
        # Get the engine
        engine = get_engine(LOG_SCHEMA)

        # Create a new connection
        connection = engine.raw_connection()
        try:
            cursor = connection.cursor()
            yield cursor
            connection.commit()
            logger.info("Transaction committed successfully")
        except Exception as e:
            connection.rollback()
            logger.error(f"Transaction rolled back due to error: {e}")
            raise e
        finally:
            cursor.close()
            connection.close()
            logger.info("Database connection closed")

    def create_translation_log(self, username: str, translation_id: str, original_filename: str, 
                              source_language: str, destination_language: str, total_questions: int) -> Optional[str]:
        """
        Create a new translation log record.

        Args:
            username: Username of the user performing the translation
            translation_id: Translation ID (UUID)
            original_filename: Original filename of the PDF
            source_language: Source language of the content
            destination_language: Target language to translate to
            total_questions: Total number of questions

        Returns:
            str: Translation ID if successful, None otherwise
        """
        try:
            # Get current time
            start_time = datetime.datetime.now()

            # Insert a new record
            with self.get_connection() as cursor:
                insert_query = f"""
                    INSERT INTO {LOG_SCHEMA}.mcq_translation_log
                    (username, translationId, originalFileName, sourceLanguage, destinationLanguage, 
                     totalQuestions, startTime, status)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """

                # Log the query
                logger.info(f"Creating translation log with translation_id: {translation_id}")

                # Execute the query
                cursor.execute(insert_query, (
                    username,
                    translation_id,
                    original_filename,
                    source_language,
                    destination_language,
                    total_questions,
                    start_time,
                    'started'
                ))

                # Log the result
                rows_affected = cursor.rowcount
                logger.info(f"Translation log creation query affected {rows_affected} rows")

            return translation_id
        except Exception as e:
            logger.error(f"Error creating translation log: {e}")
            return None

    def update_translation_status(self, translation_id: str, status: str, translated_s3_path: str = None) -> bool:
        """
        Update the status of a translation log record.

        Args:
            translation_id: Translation ID to update
            status: New status ('completed', 'failed', etc.)
            translated_s3_path: S3 path to the translated file (optional)

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Update the record
            with self.get_connection() as cursor:
                if translated_s3_path:
                    update_query = f"""
                        UPDATE {LOG_SCHEMA}.mcq_translation_log
                        SET status = %s, translateds3path = %s
                        WHERE translationId = %s
                    """
                    cursor.execute(update_query, (status, translated_s3_path, translation_id))
                else:
                    update_query = f"""
                        UPDATE {LOG_SCHEMA}.mcq_translation_log
                        SET status = %s
                        WHERE translationId = %s
                    """
                    cursor.execute(update_query, (status, translation_id))

                # Log the result
                rows_affected = cursor.rowcount
                logger.info(f"Translation status update query affected {rows_affected} rows")

                if rows_affected == 0:
                    logger.warning(f"No translation log found with translation_id: {translation_id}")
                    return False

                return True
        except Exception as e:
            logger.error(f"Error updating translation status: {e}")
            return False

    def get_translation_status(self, translation_id: str) -> dict:
        """
        Get the status of a translation log record.

        Args:
            translation_id: Translation ID to query

        Returns:
            dict: Translation status details
        """
        try:
            with self.get_connection() as cursor:
                query = f"""
                    SELECT username, originalFileName, sourceLanguage, destinationLanguage, 
                           totalQuestions, startTime, status, translateds3path
                    FROM {LOG_SCHEMA}.mcq_translation_log
                    WHERE translationId = %s
                    LIMIT 1
                """

                # Log the query
                logger.info(f"Executing query: {query} with params: ({translation_id},)")

                # Execute the query
                cursor.execute(query, (translation_id,))

                # Fetch the result
                result = cursor.fetchone()

                if not result:
                    logger.warning(f"No translation log found with translation_id: {translation_id}")
                    return {"status": "not_found", "message": f"No translation log found with ID {translation_id}"}

                # Convert result to dictionary
                return {
                    "status": "found",
                    "username": result[0],
                    "original_filename": result[1],
                    "source_language": result[2],
                    "destination_language": result[3],
                    "total_questions": result[4],
                    "start_time": result[5],
                    "translation_status": result[6],
                    "translated_s3_path": result[7]
                }

        except Exception as e:
            logger.error(f"Error getting translation status: {e}")
            return {"status": "error", "message": str(e)}
